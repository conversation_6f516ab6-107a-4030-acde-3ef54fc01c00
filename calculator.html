<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>计算器</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Arial', sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            display: flex;
            justify-content: center;
            align-items: center;
            min-height: 100vh;
            padding: 20px;
        }

        .calculator {
            background: #2c3e50;
            border-radius: 20px;
            padding: 30px;
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.3);
            max-width: 400px;
            width: 100%;
        }

        .display {
            background: #34495e;
            border-radius: 10px;
            padding: 20px;
            margin-bottom: 20px;
            text-align: right;
            min-height: 80px;
            display: flex;
            flex-direction: column;
            justify-content: center;
        }

        .display-main {
            color: #ecf0f1;
            font-size: 2.5em;
            font-weight: bold;
            word-wrap: break-word;
            overflow-wrap: break-word;
        }

        .display-history {
            color: #95a5a6;
            font-size: 1em;
            margin-bottom: 10px;
            min-height: 20px;
        }

        .buttons {
            display: grid;
            grid-template-columns: repeat(4, 1fr);
            gap: 15px;
        }

        button {
            border: none;
            border-radius: 10px;
            font-size: 1.2em;
            font-weight: bold;
            height: 60px;
            cursor: pointer;
            transition: all 0.2s ease;
            outline: none;
        }

        button:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.3);
        }

        button:active {
            transform: translateY(0);
        }

        .btn-number {
            background: #3498db;
            color: white;
        }

        .btn-number:hover {
            background: #2980b9;
        }

        .btn-operator {
            background: #e74c3c;
            color: white;
        }

        .btn-operator:hover {
            background: #c0392b;
        }

        .btn-function {
            background: #f39c12;
            color: white;
        }

        .btn-function:hover {
            background: #d68910;
        }

        .btn-equals {
            background: #27ae60;
            color: white;
            grid-column: span 2;
        }

        .btn-equals:hover {
            background: #229954;
        }

        .btn-zero {
            grid-column: span 2;
        }

        .btn-clear {
            background: #e67e22;
            color: white;
        }

        .btn-clear:hover {
            background: #d35400;
        }
    </style>
</head>
<body>
    <div class="calculator">
        <div class="display">
            <div class="display-history" id="history"></div>
            <div class="display-main" id="display">0</div>
        </div>
        <div class="buttons">
            <button class="btn-clear" onclick="clearAll()">C</button>
            <button class="btn-function" onclick="clearEntry()">CE</button>
            <button class="btn-function" onclick="backspace()">⌫</button>
            <button class="btn-operator" onclick="inputOperator('/')">÷</button>
            
            <button class="btn-number" onclick="inputNumber('7')">7</button>
            <button class="btn-number" onclick="inputNumber('8')">8</button>
            <button class="btn-number" onclick="inputNumber('9')">9</button>
            <button class="btn-operator" onclick="inputOperator('*')">×</button>
            
            <button class="btn-number" onclick="inputNumber('4')">4</button>
            <button class="btn-number" onclick="inputNumber('5')">5</button>
            <button class="btn-number" onclick="inputNumber('6')">6</button>
            <button class="btn-operator" onclick="inputOperator('-')">-</button>
            
            <button class="btn-number" onclick="inputNumber('1')">1</button>
            <button class="btn-number" onclick="inputNumber('2')">2</button>
            <button class="btn-number" onclick="inputNumber('3')">3</button>
            <button class="btn-operator" onclick="inputOperator('+')">+</button>
            
            <button class="btn-number btn-zero" onclick="inputNumber('0')">0</button>
            <button class="btn-number" onclick="inputDecimal()">.</button>
            <button class="btn-equals" onclick="calculate()">=</button>
        </div>
    </div>

    <script>
        let display = document.getElementById('display');
        let history = document.getElementById('history');
        let currentInput = '0';
        let operator = null;
        let previousInput = null;
        let waitingForOperand = false;

        function updateDisplay() {
            display.textContent = currentInput;
        }

        function updateHistory(text) {
            history.textContent = text;
        }

        function inputNumber(num) {
            if (waitingForOperand) {
                currentInput = num;
                waitingForOperand = false;
            } else {
                currentInput = currentInput === '0' ? num : currentInput + num;
            }
            updateDisplay();
        }

        function inputDecimal() {
            if (waitingForOperand) {
                currentInput = '0.';
                waitingForOperand = false;
            } else if (currentInput.indexOf('.') === -1) {
                currentInput += '.';
            }
            updateDisplay();
        }

        function inputOperator(nextOperator) {
            const inputValue = parseFloat(currentInput);

            if (previousInput === null) {
                previousInput = inputValue;
            } else if (operator) {
                const currentValue = previousInput || 0;
                const newValue = performCalculation(currentValue, inputValue, operator);

                currentInput = String(newValue);
                previousInput = newValue;
                updateDisplay();
            }

            waitingForOperand = true;
            operator = nextOperator;
            updateHistory(`${previousInput} ${getOperatorSymbol(operator)}`);
        }

        function calculate() {
            const inputValue = parseFloat(currentInput);

            if (previousInput !== null && operator) {
                const newValue = performCalculation(previousInput, inputValue, operator);
                
                updateHistory(`${previousInput} ${getOperatorSymbol(operator)} ${inputValue} =`);
                currentInput = String(newValue);
                previousInput = null;
                operator = null;
                waitingForOperand = true;
                updateDisplay();
            }
        }

        function performCalculation(firstOperand, secondOperand, operator) {
            switch (operator) {
                case '+':
                    return firstOperand + secondOperand;
                case '-':
                    return firstOperand - secondOperand;
                case '*':
                    return firstOperand * secondOperand;
                case '/':
                    return secondOperand !== 0 ? firstOperand / secondOperand : 0;
                default:
                    return secondOperand;
            }
        }

        function getOperatorSymbol(op) {
            switch (op) {
                case '+': return '+';
                case '-': return '-';
                case '*': return '×';
                case '/': return '÷';
                default: return '';
            }
        }

        function clearAll() {
            currentInput = '0';
            previousInput = null;
            operator = null;
            waitingForOperand = false;
            updateDisplay();
            updateHistory('');
        }

        function clearEntry() {
            currentInput = '0';
            updateDisplay();
        }

        function backspace() {
            if (currentInput.length > 1) {
                currentInput = currentInput.slice(0, -1);
            } else {
                currentInput = '0';
            }
            updateDisplay();
        }

        // 键盘支持
        document.addEventListener('keydown', function(event) {
            const key = event.key;
            
            if (key >= '0' && key <= '9') {
                inputNumber(key);
            } else if (key === '.') {
                inputDecimal();
            } else if (key === '+' || key === '-' || key === '*' || key === '/') {
                inputOperator(key);
            } else if (key === 'Enter' || key === '=') {
                event.preventDefault();
                calculate();
            } else if (key === 'Escape') {
                clearAll();
            } else if (key === 'Backspace') {
                event.preventDefault();
                backspace();
            }
        });

        // 初始化显示
        updateDisplay();
    </script>
</body>
</html>
